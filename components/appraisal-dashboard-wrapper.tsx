"use client"

import { AppraisalDashboardTable } from "@/components/appraisal-dashboard-table"
import { bulkAppraisalAction } from "@/lib/actions"
import { toast } from "sonner"
import { getManagers } from "@/lib/data/managers"
import type { EmployeeAppraisal, Manager } from "@/lib/types"
import React from "react"

interface AppraisalDashboardWrapperProps {
  data: EmployeeAppraisal[]
}

export function AppraisalDashboardWrapper({ data }: AppraisalDashboardWrapperProps) {
  const [managers, setManagers] = React.useState<Manager[]>([])
  const [loading, setLoading] = React.useState(true)

  React.useEffect(() => {
    const fetchManagers = async () => {
      try {
        const managersData = await getManagers()
        setManagers(managersData)
      } catch (error) {
        console.error('Failed to fetch managers:', error)
        setManagers([])
      } finally {
        setLoading(false)
      }
    }

    fetchManagers()
  }, [])

  const handleBulkAction = async (action: string, selectedIds: string[]) => {
    try {
      const result = await bulkAppraisalAction(action, selectedIds)
      if (result.success) {
        toast.success(result.message || 'Action completed successfully')
        // Revalidate the page to show updated data
        window.location.reload()
      } else {
        toast.error(result.error || 'Action failed')
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Bulk action error:', error)
    }
  }

  if (loading) {
    return <div className="flex items-center justify-center p-8">Loading managers...</div>
  }

  return <AppraisalDashboardTable data={data} managers={managers} onBulkAction={handleBulkAction} />
}